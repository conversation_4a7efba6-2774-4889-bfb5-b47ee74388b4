using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using HarmoniHSE360.Application.Common.Interfaces;
using HarmoniHSE360.Application.Features.Health.DTOs;
using HarmoniHSE360.Domain.Entities;

namespace HarmoniHSE360.Application.Features.Health.Queries;

public class GetHealthRiskAssessmentQueryHandler : IRequestHandler<GetHealthRiskAssessmentQuery, HealthRiskAssessmentDto>
{
    private readonly IApplicationDbContext _context;
    private readonly ILogger<GetHealthRiskAssessmentQueryHandler> _logger;
    private readonly ICacheService _cache;

    public GetHealthRiskAssessmentQueryHandler(
        IApplicationDbContext context,
        ILogger<GetHealthRiskAssessmentQueryHandler> logger,
        ICacheService cache)
    {
        _context = context;
        _logger = logger;
        _cache = cache;
    }

    public async Task<HealthRiskAssessmentDto> Handle(GetHealthRiskAssessmentQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Performing health risk assessment for date range: {FromDate} to {ToDate}, Department: {Department}, PersonType: {PersonType}, Scope: {Scope}",
            request.FromDate?.ToString("yyyy-MM-dd") ?? "All",
            request.ToDate?.ToString("yyyy-MM-dd") ?? "All",
            request.Department ?? "All",
            request.PersonType?.ToString() ?? "All",
            request.Scope);

        // Set default date range
        var fromDate = request.FromDate ?? DateTime.UtcNow.AddYears(-1);
        var toDate = request.ToDate ?? DateTime.UtcNow;

        // Try cache first
        var cacheKey = $"health-risk-assessment-{fromDate:yyyyMMdd}-{toDate:yyyyMMdd}-{request.Department ?? "all"}-{request.PersonType?.ToString() ?? "all"}-{request.Scope}-{request.IncludeInactive}-{request.IncludePredictiveAnalysis}";
        var cachedResult = await _cache.GetAsync<HealthRiskAssessmentDto>(cacheKey);
        if (cachedResult != null)
        {
            _logger.LogInformation("Health risk assessment data found in cache");
            return cachedResult;
        }

        // Build base query for health records
        var healthRecordsQuery = _context.HealthRecords
            .Include(hr => hr.Person)
            .Include(hr => hr.MedicalConditions.Where(mc => mc.IsActive))
            .Include(hr => hr.Vaccinations)
            .Include(hr => hr.HealthIncidents.Where(hi => hi.IncidentDateTime >= fromDate && hi.IncidentDateTime <= toDate))
            .Include(hr => hr.EmergencyContacts.Where(ec => ec.IsActive))
            .AsQueryable();

        if (!request.IncludeInactive)
        {
            healthRecordsQuery = healthRecordsQuery.Where(hr => hr.IsActive);
        }

        if (request.PersonType.HasValue)
        {
            healthRecordsQuery = healthRecordsQuery.Where(hr => hr.PersonType == request.PersonType.Value);
        }

        if (!string.IsNullOrEmpty(request.Department))
        {
            healthRecordsQuery = healthRecordsQuery.Where(hr =>
                hr.Person.Department != null &&
                hr.Person.Department.ToLower().Contains(request.Department.ToLower()));
        }

        var healthRecords = await healthRecordsQuery.ToListAsync(cancellationToken);

        // Calculate basic risk metrics
        var totalPopulation = healthRecords.Count;
        var riskLevels = CalculateIndividualRiskLevels(healthRecords);
        
        var highRiskCount = riskLevels.Count(r => r.RiskLevel == "High");
        var mediumRiskCount = riskLevels.Count(r => r.RiskLevel == "Medium");
        var lowRiskCount = riskLevels.Count(r => r.RiskLevel == "Low");

        var overallRiskScore = CalculateOverallRiskScore(riskLevels);

        // Critical conditions analysis
        var criticalConditionsRisks = AnalyzeCriticalConditionsRisk(healthRecords);

        // Vaccination compliance risks
        var vaccinationRisks = AnalyzeVaccinationRisks(healthRecords);

        // Health incident patterns
        var incidentRisks = AnalyzeIncidentRisks(healthRecords, fromDate, toDate);

        // Emergency preparedness assessment
        var emergencyPreparedness = AssessEmergencyPreparedness(healthRecords);

        // Department risk analysis
        var departmentRisks = AnalyzeDepartmentRisks(healthRecords);

        // Age group analysis (for comprehensive scope)
        var ageGroupRisks = request.Scope == RiskAssessmentScope.Comprehensive ? 
            AnalyzeAgeGroupRisks(healthRecords) : new List<AgeGroupRiskAnalysis>();

        // Predictive analysis (if requested)
        var predictiveInsights = request.IncludePredictiveAnalysis ? 
            GeneratePredictiveInsights(healthRecords, riskLevels) : new PredictiveHealthInsights();

        // Risk mitigation recommendations
        var recommendations = GenerateRiskMitigationRecommendations(
            riskLevels, criticalConditionsRisks, vaccinationRisks, incidentRisks, emergencyPreparedness);

        // High-priority individuals requiring attention
        var highPriorityIndividuals = riskLevels
            .Where(r => r.RiskLevel == "High" || r.RiskFactors.Count >= 3)
            .OrderByDescending(r => r.RiskScore)
            .Take(20)
            .Select(r =>
            {
                var healthRecord = healthRecords.First(hr => hr.Id == r.HealthRecordId);
                return new HighRiskIndividualSummary
                {
                    HealthRecordId = r.HealthRecordId,
                    PersonName = healthRecord.Person.Name,
                    PersonType = healthRecord.PersonType.ToString(),
                    Department = healthRecord.Person.Department,
                    RiskScore = r.RiskScore,
                    RiskLevel = r.RiskLevel,
                    PrimaryRiskFactors = r.RiskFactors.Take(3).ToList(),
                    CriticalConditions = healthRecord.MedicalConditions
                        .Where(mc => mc.IsActive && mc.RequiresEmergencyAction)
                        .Select(mc => mc.Name)
                        .ToList(),
                    RecentIncidents = healthRecord.HealthIncidents
                        .Where(hi => hi.IncidentDateTime >= DateTime.UtcNow.AddDays(-90))
                        .Count(),
                    VaccinationCompliance = CalculatePersonVaccinationCompliance(healthRecord),
                    LastHealthUpdate = healthRecord.LastModifiedAt ?? healthRecord.CreatedAt
                };
            })
            .ToList();

        // Risk trend analysis
        var riskTrends = request.Scope >= RiskAssessmentScope.Standard ?
            AnalyzeRiskTrends(healthRecords, fromDate, toDate) : new List<RiskTrendDataPoint>();

        var result = new HealthRiskAssessmentDto
        {
            TotalPopulation = totalPopulation,
            HighRiskCount = highRiskCount,
            MediumRiskCount = mediumRiskCount,
            LowRiskCount = lowRiskCount,
            OverallRiskScore = overallRiskScore,
            RiskDistribution = new RiskDistribution
            {
                HighRiskPercentage = totalPopulation > 0 ? (decimal)highRiskCount / totalPopulation * 100 : 0,
                MediumRiskPercentage = totalPopulation > 0 ? (decimal)mediumRiskCount / totalPopulation * 100 : 0,
                LowRiskPercentage = totalPopulation > 0 ? (decimal)lowRiskCount / totalPopulation * 100 : 0
            },

            CriticalConditionsRisk = criticalConditionsRisks,
            VaccinationRisk = vaccinationRisks,
            IncidentRisk = incidentRisks,
            EmergencyPreparedness = emergencyPreparedness,

            DepartmentRisks = departmentRisks,
            AgeGroupRisks = ageGroupRisks,
            RiskTrends = riskTrends,

            HighPriorityIndividuals = highPriorityIndividuals,
            PredictiveInsights = predictiveInsights,
            Recommendations = recommendations,

            AssessmentScope = request.Scope.ToString(),
            FromDate = fromDate,
            ToDate = toDate,
            GeneratedAt = DateTime.UtcNow
        };

        // Cache for 4 hours (risk assessments are complex and resource-intensive)
        await _cache.SetAsync(cacheKey, result, TimeSpan.FromHours(4), new[] { "health", "health-risk-assessment" });

        _logger.LogInformation("Health risk assessment completed. Total population: {TotalPopulation}, High risk: {HighRisk}, Overall risk score: {RiskScore}",
            totalPopulation, highRiskCount, overallRiskScore);

        return result;
    }

    private List<IndividualRiskAssessment> CalculateIndividualRiskLevels(List<HealthRecord> healthRecords)
    {
        return healthRecords.Select(hr =>
        {
            var riskFactors = new List<string>();
            var riskScore = 0;

            // Critical medical conditions
            var criticalConditions = hr.MedicalConditions.Where(mc => mc.IsActive && mc.RequiresEmergencyAction).ToList();
            if (criticalConditions.Any())
            {
                riskFactors.Add($"Critical medical conditions ({criticalConditions.Count})");
                riskScore += criticalConditions.Count * 20;
            }

            // Life-threatening conditions
            var lifeThreatening = hr.MedicalConditions.Where(mc => mc.IsActive && mc.Severity == ConditionSeverity.LifeThreatening).ToList();
            if (lifeThreatening.Any())
            {
                riskFactors.Add($"Life-threatening conditions ({lifeThreatening.Count})");
                riskScore += lifeThreatening.Count * 30;
            }

            // Vaccination non-compliance
            var requiredVaccinations = hr.Vaccinations.Where(v => v.IsRequired).ToList();
            var nonCompliantVaccinations = requiredVaccinations.Where(v => !v.IsCompliant()).ToList();
            if (nonCompliantVaccinations.Any())
            {
                riskFactors.Add($"Non-compliant vaccinations ({nonCompliantVaccinations.Count})");
                riskScore += nonCompliantVaccinations.Count * 10;
            }

            // Recent health incidents
            var recentIncidents = hr.HealthIncidents.Where(hi => hi.IncidentDateTime >= DateTime.UtcNow.AddDays(-180)).ToList();
            if (recentIncidents.Any())
            {
                riskFactors.Add($"Recent health incidents ({recentIncidents.Count})");
                riskScore += recentIncidents.Count * 15;
            }

            // Critical incidents
            var criticalIncidents = recentIncidents.Where(hi => hi.IsCritical()).ToList();
            if (criticalIncidents.Any())
            {
                riskFactors.Add($"Critical incidents ({criticalIncidents.Count})");
                riskScore += criticalIncidents.Count * 25;
            }

            // Missing emergency contacts
            if (!hr.EmergencyContacts.Any(ec => ec.IsActive))
            {
                riskFactors.Add("No emergency contacts");
                riskScore += 20;
            }

            // Age-based risk (if DOB available)
            if (hr.DateOfBirth.HasValue)
            {
                var age = DateTime.UtcNow.Year - hr.DateOfBirth.Value.Year;
                if (age < 5 || age > 65)
                {
                    riskFactors.Add($"Age-related risk (age {age})");
                    riskScore += 10;
                }
            }

            // Determine risk level
            var riskLevel = riskScore switch
            {
                >= 60 => "High",
                >= 30 => "Medium",
                _ => "Low"
            };

            return new IndividualRiskAssessment
            {
                HealthRecordId = hr.Id,
                RiskScore = riskScore,
                RiskLevel = riskLevel,
                RiskFactors = riskFactors
            };
        }).ToList();
    }

    private decimal CalculateOverallRiskScore(List<IndividualRiskAssessment> riskLevels)
    {
        if (!riskLevels.Any()) return 0;
        return riskLevels.Average(r => r.RiskScore);
    }

    private CriticalConditionsRisk AnalyzeCriticalConditionsRisk(List<HealthRecord> healthRecords)
    {
        var allConditions = healthRecords.SelectMany(hr => hr.MedicalConditions.Where(mc => mc.IsActive)).ToList();
        var criticalConditions = allConditions.Where(mc => mc.RequiresEmergencyAction).ToList();
        var lifeThreatening = allConditions.Where(mc => mc.Severity == ConditionSeverity.LifeThreatening).ToList();

        var conditionsByType = criticalConditions
            .GroupBy(mc => mc.Type)
            .Select(g => new ConditionTypeRisk
            {
                Type = g.Key.ToString(),
                Count = g.Count(),
                AffectedPeople = g.Select(mc => mc.HealthRecordId).Distinct().Count(),
                RiskLevel = DetermineCriticalConditionRiskLevel(g.Key, g.Count())
            })
            .OrderByDescending(c => c.Count)
            .ToList();

        return new CriticalConditionsRisk
        {
            TotalCriticalConditions = criticalConditions.Count,
            PeopleWithCriticalConditions = criticalConditions.Select(mc => mc.HealthRecordId).Distinct().Count(),
            LifeThreateningConditions = lifeThreatening.Count,
            ConditionsByType = conditionsByType,
            RiskLevel = DetermineOverallCriticalConditionRisk(criticalConditions.Count, healthRecords.Count)
        };
    }

    private VaccinationRisk AnalyzeVaccinationRisks(List<HealthRecord> healthRecords)
    {
        var allVaccinations = healthRecords.SelectMany(hr => hr.Vaccinations).ToList();
        var requiredVaccinations = allVaccinations.Where(v => v.IsRequired).ToList();
        var nonCompliantVaccinations = requiredVaccinations.Where(v => !v.IsCompliant()).ToList();
        var expiredVaccinations = allVaccinations.Where(v => v.IsExpired()).ToList();
        var expiringSoon = allVaccinations.Where(v => v.IsExpiringSoon(30)).ToList();

        var complianceRate = requiredVaccinations.Any() ? 
            (decimal)(requiredVaccinations.Count - nonCompliantVaccinations.Count) / requiredVaccinations.Count * 100 : 100;

        return new VaccinationRisk
        {
            TotalRequiredVaccinations = requiredVaccinations.Count,
            NonCompliantVaccinations = nonCompliantVaccinations.Count,
            ExpiredVaccinations = expiredVaccinations.Count,
            ExpiringSoonVaccinations = expiringSoon.Count,
            ComplianceRate = complianceRate,
            PeopleAtRisk = nonCompliantVaccinations.Select(v => v.HealthRecordId).Distinct().Count(),
            RiskLevel = DetermineVaccinationRiskLevel(complianceRate)
        };
    }

    private IncidentRisk AnalyzeIncidentRisks(List<HealthRecord> healthRecords, DateTime fromDate, DateTime toDate)
    {
        var allIncidents = healthRecords.SelectMany(hr => hr.HealthIncidents).ToList();
        var criticalIncidents = allIncidents.Where(hi => hi.IsCritical()).ToList();
        var hospitalizations = allIncidents.Where(hi => hi.RequiredHospitalization).ToList();
        var unresolvedIncidents = allIncidents.Where(hi => !hi.IsResolved).ToList();

        var incidentRate = healthRecords.Any() ? (decimal)allIncidents.Count / healthRecords.Count : 0;
        var criticalIncidentRate = allIncidents.Any() ? (decimal)criticalIncidents.Count / allIncidents.Count * 100 : 0;

        return new IncidentRisk
        {
            TotalIncidents = allIncidents.Count,
            CriticalIncidents = criticalIncidents.Count,
            Hospitalizations = hospitalizations.Count,
            UnresolvedIncidents = unresolvedIncidents.Count,
            IncidentRate = incidentRate,
            CriticalIncidentRate = criticalIncidentRate,
            RiskLevel = DetermineIncidentRiskLevel(criticalIncidentRate, incidentRate)
        };
    }

    private EmergencyPreparedness AssessEmergencyPreparedness(List<HealthRecord> healthRecords)
    {
        var totalPeople = healthRecords.Count;
        var withContacts = healthRecords.Count(hr => hr.EmergencyContacts.Any(ec => ec.IsActive));
        var withPrimaryContacts = healthRecords.Count(hr => hr.EmergencyContacts.Any(ec => ec.IsActive && ec.IsPrimaryContact));
        var withMedicalAuth = healthRecords.Count(hr => hr.EmergencyContacts.Any(ec => ec.IsActive && ec.AuthorizedForMedicalDecisions));

        var preparednessScore = totalPeople > 0 ? (decimal)withContacts / totalPeople * 100 : 0;

        return new EmergencyPreparedness
        {
            PeopleWithEmergencyContacts = withContacts,
            PeopleWithPrimaryContacts = withPrimaryContacts,
            PeopleWithMedicalAuthorization = withMedicalAuth,
            PreparednessScore = preparednessScore,
            RiskLevel = DetermineEmergencyPreparednessRisk(preparednessScore)
        };
    }

    private List<DepartmentRiskAnalysis> AnalyzeDepartmentRisks(List<HealthRecord> healthRecords)
    {
        return healthRecords
            .Where(hr => !string.IsNullOrEmpty(hr.Person.Department))
            .GroupBy(hr => hr.Person.Department)
            .Select(g =>
            {
                var deptHealthRecords = g.ToList();
                var riskLevels = CalculateIndividualRiskLevels(deptHealthRecords);
                var avgRiskScore = riskLevels.Any() ? riskLevels.Average(r => r.RiskScore) : 0;

                return new DepartmentRiskAnalysis
                {
                    Department = g.Key!,
                    TotalPeople = deptHealthRecords.Count,
                    HighRiskCount = riskLevels.Count(r => r.RiskLevel == "High"),
                    AverageRiskScore = avgRiskScore,
                    CriticalConditions = deptHealthRecords.Sum(hr => hr.MedicalConditions.Count(mc => mc.IsActive && mc.RequiresEmergencyAction)),
                    RecentIncidents = deptHealthRecords.Sum(hr => hr.HealthIncidents.Count(hi => hi.IncidentDateTime >= DateTime.UtcNow.AddDays(-90))),
                    VaccinationCompliance = CalculateDepartmentVaccinationCompliance(deptHealthRecords),
                    RiskLevel = DetermineDepartmentRiskLevel(avgRiskScore)
                };
            })
            .OrderByDescending(d => d.AverageRiskScore)
            .ToList();
    }

    private List<AgeGroupRiskAnalysis> AnalyzeAgeGroupRisks(List<HealthRecord> healthRecords)
    {
        var ageGroups = new[]
        {
            new { Name = "0-5", Min = 0, Max = 5 },
            new { Name = "6-12", Min = 6, Max = 12 },
            new { Name = "13-18", Min = 13, Max = 18 },
            new { Name = "19-30", Min = 19, Max = 30 },
            new { Name = "31-50", Min = 31, Max = 50 },
            new { Name = "51-65", Min = 51, Max = 65 },
            new { Name = "65+", Min = 65, Max = 150 }
        };

        return ageGroups.Select(ag =>
        {
            var ageGroupRecords = healthRecords
                .Where(hr => hr.DateOfBirth.HasValue)
                .Where(hr =>
                {
                    var age = DateTime.UtcNow.Year - hr.DateOfBirth!.Value.Year;
                    return age >= ag.Min && age <= ag.Max;
                })
                .ToList();

            var riskLevels = CalculateIndividualRiskLevels(ageGroupRecords);
            var avgRiskScore = riskLevels.Any() ? riskLevels.Average(r => r.RiskScore) : 0;

            return new AgeGroupRiskAnalysis
            {
                AgeGroup = ag.Name,
                TotalPeople = ageGroupRecords.Count,
                HighRiskCount = riskLevels.Count(r => r.RiskLevel == "High"),
                AverageRiskScore = avgRiskScore,
                CommonRiskFactors = IdentifyCommonRiskFactors(riskLevels),
                RiskLevel = DetermineAgeGroupRiskLevel(avgRiskScore)
            };
        }).Where(ag => ag.TotalPeople > 0).ToList();
    }

    private PredictiveHealthInsights GeneratePredictiveInsights(List<HealthRecord> healthRecords, List<IndividualRiskAssessment> riskLevels)
    {
        var insights = new List<string>();
        var riskFactors = new List<string>();

        // Analyze trends
        var highRiskPercentage = healthRecords.Any() ? (decimal)riskLevels.Count(r => r.RiskLevel == "High") / healthRecords.Count * 100 : 0;
        if (highRiskPercentage > 15)
        {
            insights.Add("High percentage of individuals at high risk suggests need for population-level intervention");
            riskFactors.Add("Elevated population risk level");
        }

        // Vaccination expiry predictions
        var allVaccinations = healthRecords.SelectMany(hr => hr.Vaccinations).ToList();
        var expiringSoon = allVaccinations.Count(v => v.IsExpiringSoon(90));
        if (expiringSoon > allVaccinations.Count * 0.1m)
        {
            insights.Add($"Approximately {expiringSoon} vaccinations will expire in the next 90 days");
            riskFactors.Add("Upcoming vaccination expiries");
        }

        // Incident pattern analysis
        var recentIncidents = healthRecords.SelectMany(hr => hr.HealthIncidents.Where(hi => hi.IncidentDateTime >= DateTime.UtcNow.AddDays(-90))).ToList();
        if (recentIncidents.Count > healthRecords.Count * 0.05m)
        {
            insights.Add("Recent incident rate suggests increased health risks in population");
            riskFactors.Add("Elevated incident frequency");
        }

        return new PredictiveHealthInsights
        {
            Insights = insights,
            IdentifiedRiskFactors = riskFactors,
            RecommendedActions = GeneratePredictiveRecommendations(insights),
            ConfidenceLevel = insights.Any() ? "Medium" : "Low"
        };
    }

    private List<string> GenerateRiskMitigationRecommendations(
        List<IndividualRiskAssessment> riskLevels,
        CriticalConditionsRisk criticalConditionsRisk,
        VaccinationRisk vaccinationRisk,
        IncidentRisk incidentRisk,
        EmergencyPreparedness emergencyPreparedness)
    {
        var recommendations = new List<string>();

        var highRiskCount = riskLevels.Count(r => r.RiskLevel == "High");
        if (highRiskCount > riskLevels.Count * 0.1m)
        {
            recommendations.Add($"Implement targeted health management programs for {highRiskCount} high-risk individuals");
        }

        if (criticalConditionsRisk.RiskLevel == "High")
        {
            recommendations.Add("Enhance emergency response protocols for critical medical conditions");
        }

        if (vaccinationRisk.ComplianceRate < 85)
        {
            recommendations.Add("Launch vaccination compliance improvement campaign");
        }

        if (incidentRisk.RiskLevel == "High")
        {
            recommendations.Add("Review and strengthen incident prevention measures");
        }

        if (emergencyPreparedness.PreparednessScore < 90)
        {
            recommendations.Add("Improve emergency contact data collection and validation");
        }

        return recommendations;
    }

    private List<RiskTrendDataPoint> AnalyzeRiskTrends(List<HealthRecord> healthRecords, DateTime fromDate, DateTime toDate)
    {
        var months = Math.Min(12, (int)Math.Ceiling((toDate - fromDate).TotalDays / 30));
        
        return Enumerable.Range(0, months)
            .Select(i =>
            {
                var monthStart = fromDate.AddMonths(i);
                var monthEnd = monthStart.AddMonths(1).AddDays(-1);
                
                var monthIncidents = healthRecords
                    .SelectMany(hr => hr.HealthIncidents)
                    .Where(hi => hi.IncidentDateTime >= monthStart && hi.IncidentDateTime <= monthEnd)
                    .ToList();
                
                var criticalIncidents = monthIncidents.Count(hi => hi.IsCritical());
                var riskScore = monthIncidents.Any() ? (decimal)criticalIncidents / monthIncidents.Count * 100 : 0;

                return new RiskTrendDataPoint
                {
                    Date = monthStart,
                    IncidentCount = monthIncidents.Count,
                    CriticalIncidentCount = criticalIncidents,
                    RiskScore = riskScore
                };
            })
            .OrderBy(t => t.Date)
            .ToList();
    }

    // Helper methods for risk level determination
    private string DetermineCriticalConditionRiskLevel(ConditionType type, int count) =>
        (type, count) switch
        {
            (ConditionType.Allergy, >= 5) => "High",
            (ConditionType.Cardiac, >= 2) => "High",
            (ConditionType.Respiratory, >= 3) => "High",
            (_, >= 10) => "High",
            (_, >= 5) => "Medium",
            _ => "Low"
        };

    private string DetermineOverallCriticalConditionRisk(int criticalCount, int totalPeople) =>
        totalPeople > 0 ? (decimal)criticalCount / totalPeople * 100 switch
        {
            >= 20 => "High",
            >= 10 => "Medium",
            _ => "Low"
        } : "Low";

    private string DetermineVaccinationRiskLevel(decimal complianceRate) =>
        complianceRate switch
        {
            < 70 => "High",
            < 85 => "Medium",
            _ => "Low"
        };

    private string DetermineIncidentRiskLevel(decimal criticalIncidentRate, decimal incidentRate) =>
        (criticalIncidentRate, incidentRate) switch
        {
            (>= 25, _) => "High",
            (>= 15, _) => "Medium",
            (_, >= 0.5m) => "Medium",
            _ => "Low"
        };

    private string DetermineEmergencyPreparednessRisk(decimal preparednessScore) =>
        preparednessScore switch
        {
            < 70 => "High",
            < 90 => "Medium",
            _ => "Low"
        };

    private string DetermineDepartmentRiskLevel(decimal avgRiskScore) =>
        avgRiskScore switch
        {
            >= 60 => "High",
            >= 30 => "Medium",
            _ => "Low"
        };

    private string DetermineAgeGroupRiskLevel(decimal avgRiskScore) =>
        avgRiskScore switch
        {
            >= 50 => "High",
            >= 25 => "Medium",
            _ => "Low"
        };

    private decimal CalculatePersonVaccinationCompliance(HealthRecord healthRecord)
    {
        var requiredVaccinations = healthRecord.Vaccinations.Where(v => v.IsRequired).ToList();
        if (!requiredVaccinations.Any()) return 100;
        
        var compliant = requiredVaccinations.Count(v => v.IsCompliant());
        return (decimal)compliant / requiredVaccinations.Count * 100;
    }

    private decimal CalculateDepartmentVaccinationCompliance(List<HealthRecord> deptRecords)
    {
        var allRequired = deptRecords.SelectMany(hr => hr.Vaccinations.Where(v => v.IsRequired)).ToList();
        if (!allRequired.Any()) return 100;
        
        var compliant = allRequired.Count(v => v.IsCompliant());
        return (decimal)compliant / allRequired.Count * 100;
    }

    private List<string> IdentifyCommonRiskFactors(List<IndividualRiskAssessment> riskLevels)
    {
        return riskLevels
            .SelectMany(r => r.RiskFactors)
            .GroupBy(rf => rf)
            .Where(g => g.Count() > 1)
            .OrderByDescending(g => g.Count())
            .Take(3)
            .Select(g => g.Key)
            .ToList();
    }

    private List<string> GeneratePredictiveRecommendations(List<string> insights)
    {
        var recommendations = new List<string>();
        
        if (insights.Any(i => i.Contains("vaccination")))
        {
            recommendations.Add("Implement proactive vaccination renewal reminders");
        }
        
        if (insights.Any(i => i.Contains("incident")))
        {
            recommendations.Add("Review and strengthen preventive health measures");
        }
        
        if (insights.Any(i => i.Contains("risk")))
        {
            recommendations.Add("Consider population health intervention strategies");
        }

        return recommendations;
    }
}