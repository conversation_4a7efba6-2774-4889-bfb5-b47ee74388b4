using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.SignalR;
using MediatR;
using HarmoniHSE360.Application.Features.Health.Commands;
using HarmoniHSE360.Application.Features.Health.Queries;
using HarmoniHSE360.Application.Features.Health.DTOs;
using HarmoniHSE360.Domain.Entities;
using HarmoniHSE360.Web.Hubs;

namespace HarmoniHSE360.Web.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class HealthController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<HealthController> _logger;
    private readonly IHubContext<NotificationHub> _notificationHub;

    public HealthController(
        IMediator mediator,
        ILogger<HealthController> logger,
        IHubContext<NotificationHub> notificationHub)
    {
        _mediator = mediator;
        _logger = logger;
        _notificationHub = notificationHub;
    }

    #region Health Records Management

    /// <summary>
    /// Get paginated list of health records with filtering and search
    /// </summary>
    [HttpGet("records")]
    public async Task<ActionResult<GetHealthRecordsResponse>> GetHealthRecords(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string? searchTerm = null,
        [FromQuery] PersonType? personType = null,
        [FromQuery] string? department = null,
        [FromQuery] bool? hasCriticalConditions = null,
        [FromQuery] bool? hasExpiringVaccinations = null,
        [FromQuery] bool includeInactive = false,
        [FromQuery] string? sortBy = "CreatedAt",
        [FromQuery] bool sortDescending = true)
    {
        try
        {
            var query = new GetHealthRecordsQuery
            {
                PageNumber = pageNumber,
                PageSize = Math.Min(pageSize, 100),
                SearchTerm = searchTerm,
                PersonType = personType,
                Department = department,
                HasCriticalConditions = hasCriticalConditions,
                HasExpiringVaccinations = hasExpiringVaccinations,
                IncludeInactive = includeInactive,
                SortBy = sortBy,
                SortDescending = sortDescending
            };

            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving health records");
            return StatusCode(500, new { message = "An error occurred while retrieving health records" });
        }
    }

    /// <summary>
    /// Get health record by ID with full details
    /// </summary>
    [HttpGet("records/{id:guid}")]
    public async Task<ActionResult<HealthRecordDetailDto>> GetHealthRecord(
        Guid id,
        [FromQuery] bool includeInactive = false)
    {
        try
        {
            var query = new GetHealthRecordByIdQuery
            {
                Id = id,
                IncludeInactive = includeInactive
            };

            var result = await _mediator.Send(query);
            if (result == null)
            {
                return NotFound(new { message = "Health record not found" });
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving health record {HealthRecordId}", id);
            return StatusCode(500, new { message = "An error occurred while retrieving the health record" });
        }
    }

    /// <summary>
    /// Create a new health record
    /// </summary>
    [HttpPost("records")]
    public async Task<ActionResult<Guid>> CreateHealthRecord([FromBody] CreateHealthRecordCommand command)
    {
        try
        {
            var result = await _mediator.Send(command);
            
            // Notify via SignalR
            await _notificationHub.Clients.All.SendAsync("HealthRecordCreated", new { HealthRecordId = result });
            
            return CreatedAtAction(nameof(GetHealthRecord), new { id = result }, new { Id = result });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating health record");
            return StatusCode(500, new { message = "An error occurred while creating the health record" });
        }
    }

    /// <summary>
    /// Update an existing health record
    /// </summary>
    [HttpPut("records/{id:guid}")]
    public async Task<ActionResult> UpdateHealthRecord(Guid id, [FromBody] UpdateHealthRecordCommand command)
    {
        try
        {
            if (id != command.Id)
            {
                return BadRequest(new { message = "Health record ID mismatch" });
            }

            await _mediator.Send(command);
            
            // Notify via SignalR
            await _notificationHub.Clients.All.SendAsync("HealthRecordUpdated", new { HealthRecordId = id });
            
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating health record {HealthRecordId}", id);
            return StatusCode(500, new { message = "An error occurred while updating the health record" });
        }
    }

    /// <summary>
    /// Deactivate a health record
    /// </summary>
    [HttpDelete("records/{id:guid}")]
    public async Task<ActionResult> DeactivateHealthRecord(Guid id)
    {
        try
        {
            var command = new DeactivateHealthRecordCommand { Id = id };
            await _mediator.Send(command);
            
            // Notify via SignalR
            await _notificationHub.Clients.All.SendAsync("HealthRecordDeactivated", new { HealthRecordId = id });
            
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deactivating health record {HealthRecordId}", id);
            return StatusCode(500, new { message = "An error occurred while deactivating the health record" });
        }
    }

    #endregion

    #region Medical Conditions Management

    /// <summary>
    /// Add a medical condition to a health record
    /// </summary>
    [HttpPost("records/{healthRecordId:guid}/conditions")]
    public async Task<ActionResult<Guid>> AddMedicalCondition(
        Guid healthRecordId,
        [FromBody] AddMedicalConditionCommand command)
    {
        try
        {
            if (healthRecordId != command.HealthRecordId)
            {
                return BadRequest(new { message = "Health record ID mismatch" });
            }

            var result = await _mediator.Send(command);
            
            // Notify via SignalR if critical condition
            if (command.RequiresEmergencyAction)
            {
                await _notificationHub.Clients.All.SendAsync("CriticalMedicalConditionAdded", 
                    new { HealthRecordId = healthRecordId, ConditionId = result });
            }
            
            return CreatedAtAction(nameof(GetHealthRecord), new { id = healthRecordId }, new { Id = result });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding medical condition to health record {HealthRecordId}", healthRecordId);
            return StatusCode(500, new { message = "An error occurred while adding the medical condition" });
        }
    }

    /// <summary>
    /// Update a medical condition
    /// </summary>
    [HttpPut("conditions/{id:guid}")]
    public async Task<ActionResult> UpdateMedicalCondition(Guid id, [FromBody] UpdateMedicalConditionCommand command)
    {
        try
        {
            if (id != command.Id)
            {
                return BadRequest(new { message = "Medical condition ID mismatch" });
            }

            await _mediator.Send(command);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating medical condition {ConditionId}", id);
            return StatusCode(500, new { message = "An error occurred while updating the medical condition" });
        }
    }

    /// <summary>
    /// Remove a medical condition
    /// </summary>
    [HttpDelete("conditions/{id:guid}")]
    public async Task<ActionResult> RemoveMedicalCondition(Guid id)
    {
        try
        {
            var command = new RemoveMedicalConditionCommand { Id = id };
            await _mediator.Send(command);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing medical condition {ConditionId}", id);
            return StatusCode(500, new { message = "An error occurred while removing the medical condition" });
        }
    }

    #endregion

    #region Vaccination Management

    /// <summary>
    /// Record a vaccination
    /// </summary>
    [HttpPost("records/{healthRecordId:guid}/vaccinations")]
    public async Task<ActionResult<Guid>> RecordVaccination(
        Guid healthRecordId,
        [FromBody] RecordVaccinationCommand command)
    {
        try
        {
            if (healthRecordId != command.HealthRecordId)
            {
                return BadRequest(new { message = "Health record ID mismatch" });
            }

            var result = await _mediator.Send(command);
            
            // Notify via SignalR
            await _notificationHub.Clients.All.SendAsync("VaccinationRecorded", 
                new { HealthRecordId = healthRecordId, VaccinationId = result });
            
            return CreatedAtAction(nameof(GetHealthRecord), new { id = healthRecordId }, new { Id = result });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording vaccination for health record {HealthRecordId}", healthRecordId);
            return StatusCode(500, new { message = "An error occurred while recording the vaccination" });
        }
    }

    /// <summary>
    /// Update a vaccination record
    /// </summary>
    [HttpPut("vaccinations/{id:guid}")]
    public async Task<ActionResult> UpdateVaccination(Guid id, [FromBody] UpdateVaccinationCommand command)
    {
        try
        {
            if (id != command.Id)
            {
                return BadRequest(new { message = "Vaccination ID mismatch" });
            }

            await _mediator.Send(command);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating vaccination {VaccinationId}", id);
            return StatusCode(500, new { message = "An error occurred while updating the vaccination" });
        }
    }

    /// <summary>
    /// Set vaccination exemption
    /// </summary>
    [HttpPost("vaccinations/{id:guid}/exemption")]
    public async Task<ActionResult> SetVaccinationExemption(Guid id, [FromBody] SetVaccinationExemptionCommand command)
    {
        try
        {
            if (id != command.Id)
            {
                return BadRequest(new { message = "Vaccination ID mismatch" });
            }

            await _mediator.Send(command);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting vaccination exemption for {VaccinationId}", id);
            return StatusCode(500, new { message = "An error occurred while setting the vaccination exemption" });
        }
    }

    #endregion

    #region Dashboard and Analytics

    /// <summary>
    /// Get health dashboard with comprehensive metrics
    /// </summary>
    [HttpGet("dashboard")]
    public async Task<ActionResult<HealthDashboardDto>> GetHealthDashboard(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] string? department = null,
        [FromQuery] PersonType? personType = null,
        [FromQuery] bool includeInactive = false)
    {
        try
        {
            var query = new GetHealthDashboardQuery
            {
                FromDate = fromDate,
                ToDate = toDate,
                Department = department,
                PersonType = personType,
                IncludeInactive = includeInactive
            };

            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving health dashboard");
            return StatusCode(500, new { message = "An error occurred while retrieving the health dashboard" });
        }
    }

    /// <summary>
    /// Get vaccination compliance analysis
    /// </summary>
    [HttpGet("analytics/vaccination-compliance")]
    public async Task<ActionResult<VaccinationComplianceDto>> GetVaccinationCompliance(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] string? department = null,
        [FromQuery] PersonType? personType = null,
        [FromQuery] string? vaccineName = null,
        [FromQuery] bool includeInactive = false,
        [FromQuery] bool includeExemptions = true)
    {
        try
        {
            var query = new GetVaccinationComplianceQuery
            {
                FromDate = fromDate,
                ToDate = toDate,
                Department = department,
                PersonType = personType,
                VaccineName = vaccineName,
                IncludeInactive = includeInactive,
                IncludeExemptions = includeExemptions
            };

            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving vaccination compliance data");
            return StatusCode(500, new { message = "An error occurred while retrieving vaccination compliance data" });
        }
    }

    /// <summary>
    /// Get health incident trends and safety analysis
    /// </summary>
    [HttpGet("analytics/incident-trends")]
    public async Task<ActionResult<HealthIncidentTrendsDto>> GetHealthIncidentTrends(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] string? department = null,
        [FromQuery] PersonType? personType = null,
        [FromQuery] HealthIncidentType? incidentType = null,
        [FromQuery] HealthIncidentSeverity? severity = null,
        [FromQuery] TrendPeriod period = TrendPeriod.Monthly,
        [FromQuery] bool includeResolved = true)
    {
        try
        {
            var query = new GetHealthIncidentTrendsQuery
            {
                FromDate = fromDate,
                ToDate = toDate,
                Department = department,
                PersonType = personType,
                IncidentType = incidentType,
                Severity = severity,
                Period = period,
                IncludeResolved = includeResolved
            };

            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving health incident trends");
            return StatusCode(500, new { message = "An error occurred while retrieving health incident trends" });
        }
    }

    /// <summary>
    /// Get emergency contact validation and data quality analysis
    /// </summary>
    [HttpGet("analytics/emergency-contact-validation")]
    public async Task<ActionResult<EmergencyContactValidationDto>> GetEmergencyContactValidation(
        [FromQuery] string? department = null,
        [FromQuery] PersonType? personType = null,
        [FromQuery] bool includeInactive = false,
        [FromQuery] ValidationLevel level = ValidationLevel.Standard)
    {
        try
        {
            var query = new GetEmergencyContactValidationQuery
            {
                Department = department,
                PersonType = personType,
                IncludeInactive = includeInactive,
                Level = level
            };

            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving emergency contact validation data");
            return StatusCode(500, new { message = "An error occurred while retrieving emergency contact validation data" });
        }
    }

    /// <summary>
    /// Get comprehensive health risk assessment
    /// </summary>
    [HttpGet("analytics/risk-assessment")]
    public async Task<ActionResult<HealthRiskAssessmentDto>> GetHealthRiskAssessment(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] string? department = null,
        [FromQuery] PersonType? personType = null,
        [FromQuery] RiskAssessmentScope scope = RiskAssessmentScope.Standard,
        [FromQuery] bool includeInactive = false,
        [FromQuery] bool includePredictiveAnalysis = true)
    {
        try
        {
            var query = new GetHealthRiskAssessmentQuery
            {
                FromDate = fromDate,
                ToDate = toDate,
                Department = department,
                PersonType = personType,
                Scope = scope,
                IncludeInactive = includeInactive,
                IncludePredictiveAnalysis = includePredictiveAnalysis
            };

            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving health risk assessment");
            return StatusCode(500, new { message = "An error occurred while retrieving health risk assessment" });
        }
    }

    #endregion

    #region Health Alerts and Notifications

    /// <summary>
    /// Get active health alerts for a person or department
    /// </summary>
    [HttpGet("alerts")]
    public async Task<ActionResult<List<HealthAlert>>> GetHealthAlerts(
        [FromQuery] Guid? personId = null,
        [FromQuery] string? department = null,
        [FromQuery] AlertSeverity? severity = null,
        [FromQuery] bool activeOnly = true)
    {
        try
        {
            // This would be implemented as a separate query/command
            // For now, return a placeholder response
            var alerts = new List<HealthAlert>();
            return Ok(alerts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving health alerts");
            return StatusCode(500, new { message = "An error occurred while retrieving health alerts" });
        }
    }

    /// <summary>
    /// Trigger health emergency notification
    /// </summary>
    [HttpPost("emergency-notification")]
    public async Task<ActionResult> TriggerEmergencyNotification([FromBody] EmergencyNotificationRequest request)
    {
        try
        {
            // Notify via SignalR for immediate alert
            await _notificationHub.Clients.All.SendAsync("HealthEmergency", request);
            
            _logger.LogWarning("Health emergency notification triggered for person {PersonId}: {Message}", 
                request.PersonId, request.Message);
            
            return Ok(new { message = "Emergency notification sent successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending emergency notification");
            return StatusCode(500, new { message = "An error occurred while sending the emergency notification" });
        }
    }

    #endregion
}

// Supporting DTOs for health alerts and notifications
public class HealthAlert
{
    public Guid Id { get; set; }
    public Guid PersonId { get; set; }
    public string PersonName { get; set; } = string.Empty;
    public AlertType Type { get; set; }
    public AlertSeverity Severity { get; set; }
    public string Message { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public bool IsActive { get; set; }
}

public class EmergencyNotificationRequest
{
    public Guid PersonId { get; set; }
    public string PersonName { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public AlertSeverity Severity { get; set; }
    public string Location { get; set; } = string.Empty;
    public List<string> EmergencyContactIds { get; set; } = new();
}

public enum AlertType
{
    CriticalCondition,
    VaccinationExpiring,
    VaccinationOverdue,
    HealthIncident,
    EmergencyContactMissing,
    DataValidationIssue
}

public enum AlertSeverity
{
    Low,
    Medium,
    High,
    Critical
}