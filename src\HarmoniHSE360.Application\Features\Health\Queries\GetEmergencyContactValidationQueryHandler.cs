using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using HarmoniHSE360.Application.Common.Interfaces;
using HarmoniHSE360.Application.Features.Health.DTOs;
using HarmoniHSE360.Domain.Entities;
using System.Text.RegularExpressions;

namespace HarmoniHSE360.Application.Features.Health.Queries;

public class GetEmergencyContactValidationQueryHandler : IRequestHandler<GetEmergencyContactValidationQuery, EmergencyContactValidationDto>
{
    private readonly IApplicationDbContext _context;
    private readonly ILogger<GetEmergencyContactValidationQueryHandler> _logger;
    private readonly ICacheService _cache;

    public GetEmergencyContactValidationQueryHandler(
        IApplicationDbContext context,
        ILogger<GetEmergencyContactValidationQueryHandler> logger,
        ICacheService cache)
    {
        _context = context;
        _logger = logger;
        _cache = cache;
    }

    public async Task<EmergencyContactValidationDto> Handle(GetEmergencyContactValidationQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Validating emergency contacts for Department: {Department}, PersonType: {PersonType}, Level: {Level}",
            request.Department ?? "All",
            request.PersonType?.ToString() ?? "All",
            request.Level);

        // Try cache first
        var cacheKey = $"emergency-contact-validation-{request.Department ?? "all"}-{request.PersonType?.ToString() ?? "all"}-{request.IncludeInactive}-{request.Level}";
        var cachedResult = await _cache.GetAsync<EmergencyContactValidationDto>(cacheKey);
        if (cachedResult != null)
        {
            _logger.LogInformation("Emergency contact validation data found in cache");
            return cachedResult;
        }

        // Build base query for health records
        var healthRecordsQuery = _context.HealthRecords
            .Include(hr => hr.Person)
            .Include(hr => hr.EmergencyContacts.Where(ec => request.IncludeInactive || ec.IsActive))
            .AsQueryable();

        if (!request.IncludeInactive)
        {
            healthRecordsQuery = healthRecordsQuery.Where(hr => hr.IsActive);
        }

        if (request.PersonType.HasValue)
        {
            healthRecordsQuery = healthRecordsQuery.Where(hr => hr.PersonType == request.PersonType.Value);
        }

        if (!string.IsNullOrEmpty(request.Department))
        {
            healthRecordsQuery = healthRecordsQuery.Where(hr =>
                hr.Person.Department != null &&
                hr.Person.Department.ToLower().Contains(request.Department.ToLower()));
        }

        var healthRecords = await healthRecordsQuery.ToListAsync(cancellationToken);

        // Calculate basic metrics
        var totalPeople = healthRecords.Count;
        var peopleWithContacts = healthRecords.Count(hr => hr.EmergencyContacts.Any(ec => ec.IsActive));
        var peopleWithoutContacts = totalPeople - peopleWithContacts;
        var peopleWithPrimaryContacts = healthRecords.Count(hr => hr.EmergencyContacts.Any(ec => ec.IsActive && ec.IsPrimaryContact));
        var completenessRate = totalPeople > 0 ? (decimal)peopleWithContacts / totalPeople * 100 : 0;

        // Detailed validation by person
        var validationResults = healthRecords.Select(hr => ValidatePersonContacts(hr, request.Level)).ToList();

        // Aggregate validation issues
        var totalIssues = validationResults.Sum(v => v.Issues.Count);
        var criticalIssues = validationResults.Sum(v => v.Issues.Count(i => i.Severity == "Critical"));
        var warningIssues = validationResults.Sum(v => v.Issues.Count(i => i.Severity == "Warning"));

        // Issue categories breakdown
        var issuesByCategory = validationResults
            .SelectMany(v => v.Issues)
            .GroupBy(i => i.Category)
            .Select(g => new ValidationIssueCategoryBreakdown
            {
                Category = g.Key,
                Count = g.Count(),
                CriticalCount = g.Count(i => i.Severity == "Critical"),
                WarningCount = g.Count(i => i.Severity == "Warning"),
                Percentage = totalIssues > 0 ? (decimal)g.Count() / totalIssues * 100 : 0,
                AffectedPeople = g.Select(i => i.PersonId).Distinct().Count()
            })
            .OrderByDescending(c => c.Count)
            .ToList();

        // Department breakdown
        var departmentBreakdown = healthRecords
            .Where(hr => !string.IsNullOrEmpty(hr.Person.Department))
            .GroupBy(hr => hr.Person.Department)
            .Select(g =>
            {
                var deptValidations = validationResults.Where(v => 
                    healthRecords.Any(hr => hr.Id == v.HealthRecordId && hr.Person.Department == g.Key)).ToList();
                
                return new DepartmentContactValidationBreakdown
                {
                    Department = g.Key!,
                    TotalPeople = g.Count(),
                    PeopleWithContacts = g.Count(hr => hr.EmergencyContacts.Any(ec => ec.IsActive)),
                    CompletionRate = g.Any() ? (decimal)g.Count(hr => hr.EmergencyContacts.Any(ec => ec.IsActive)) / g.Count() * 100 : 0,
                    TotalIssues = deptValidations.Sum(v => v.Issues.Count),
                    CriticalIssues = deptValidations.Sum(v => v.Issues.Count(i => i.Severity == "Critical")),
                    ValidationScore = CalculateDepartmentValidationScore(deptValidations)
                };
            })
            .OrderByDescending(d => d.ValidationScore)
            .ToList();

        // Person type breakdown
        var personTypeBreakdown = healthRecords
            .GroupBy(hr => hr.PersonType)
            .Select(g =>
            {
                var typeValidations = validationResults.Where(v => 
                    healthRecords.Any(hr => hr.Id == v.HealthRecordId && hr.PersonType == g.Key)).ToList();
                
                return new PersonTypeContactValidationBreakdown
                {
                    PersonType = g.Key.ToString(),
                    TotalPeople = g.Count(),
                    PeopleWithContacts = g.Count(hr => hr.EmergencyContacts.Any(ec => ec.IsActive)),
                    CompletionRate = g.Any() ? (decimal)g.Count(hr => hr.EmergencyContacts.Any(ec => ec.IsActive)) / g.Count() * 100 : 0,
                    TotalIssues = typeValidations.Sum(v => v.Issues.Count),
                    CriticalIssues = typeValidations.Sum(v => v.Issues.Count(i => i.Severity == "Critical")),
                    ValidationScore = CalculatePersonTypeValidationScore(typeValidations)
                };
            })
            .OrderByDescending(p => p.ValidationScore)
            .ToList();

        // People requiring immediate attention
        var priorityValidationIssues = validationResults
            .Where(v => v.Issues.Any(i => i.Severity == "Critical") || v.ValidationScore < 50)
            .OrderBy(v => v.ValidationScore)
            .Take(20)
            .Select(v =>
            {
                var healthRecord = healthRecords.First(hr => hr.Id == v.HealthRecordId);
                return new PersonValidationSummary
                {
                    HealthRecordId = v.HealthRecordId,
                    PersonName = healthRecord.Person.Name,
                    PersonType = healthRecord.PersonType.ToString(),
                    Department = healthRecord.Person.Department,
                    ValidationScore = v.ValidationScore,
                    CriticalIssues = v.Issues.Count(i => i.Severity == "Critical"),
                    TotalIssues = v.Issues.Count,
                    TopIssues = v.Issues.Take(3).Select(i => i.Description).ToList(),
                    LastUpdated = healthRecord.EmergencyContacts.Any() ? 
                        healthRecord.EmergencyContacts.Max(ec => ec.LastModifiedAt ?? ec.CreatedAt) : 
                        healthRecord.CreatedAt
                };
            })
            .ToList();

        // Contact information quality metrics
        var allContacts = healthRecords.SelectMany(hr => hr.EmergencyContacts.Where(ec => ec.IsActive)).ToList();
        var contactQualityMetrics = new ContactQualityMetrics
        {
            TotalContacts = allContacts.Count,
            ContactsWithValidPhone = allContacts.Count(ec => IsValidPhoneNumber(ec.PrimaryPhone)),
            ContactsWithValidEmail = allContacts.Count(ec => IsValidEmail(ec.Email)),
            ContactsWithBothPhoneAndEmail = allContacts.Count(ec => 
                IsValidPhoneNumber(ec.PrimaryPhone) && IsValidEmail(ec.Email)),
            ContactsWithSecondaryPhone = allContacts.Count(ec => !string.IsNullOrEmpty(ec.SecondaryPhone)),
            ContactsWithAddress = allContacts.Count(ec => !string.IsNullOrEmpty(ec.Address)),
            AuthorizedForPickup = allContacts.Count(ec => ec.AuthorizedForPickup),
            AuthorizedForMedical = allContacts.Count(ec => ec.AuthorizedForMedicalDecisions),
            DuplicatePhoneNumbers = IdentifyDuplicateContacts(allContacts)
        };

        // Generate recommendations
        var recommendations = GenerateRecommendations(validationResults, departmentBreakdown, contactQualityMetrics);

        var result = new EmergencyContactValidationDto
        {
            TotalPeople = totalPeople,
            PeopleWithContacts = peopleWithContacts,
            PeopleWithoutContacts = peopleWithoutContacts,
            PeopleWithPrimaryContacts = peopleWithPrimaryContacts,
            CompletionRate = completenessRate,
            
            TotalIssues = totalIssues,
            CriticalIssues = criticalIssues,
            WarningIssues = warningIssues,
            OverallValidationScore = CalculateOverallValidationScore(validationResults),
            
            IssuesByCategory = issuesByCategory,
            DepartmentBreakdown = departmentBreakdown,
            PersonTypeBreakdown = personTypeBreakdown,
            PriorityValidationIssues = priorityValidationIssues,
            ContactQualityMetrics = contactQualityMetrics,
            Recommendations = recommendations,
            
            ValidationLevel = request.Level.ToString(),
            GeneratedAt = DateTime.UtcNow
        };

        // Cache for 2 hours
        await _cache.SetAsync(cacheKey, result, TimeSpan.FromHours(2), new[] { "health", "emergency-contact-validation" });

        _logger.LogInformation("Emergency contact validation completed. Total people: {TotalPeople}, Completion rate: {CompletionRate}%, Critical issues: {CriticalIssues}",
            totalPeople, completenessRate, criticalIssues);

        return result;
    }

    private PersonContactValidationResult ValidatePersonContacts(HealthRecord healthRecord, ValidationLevel level)
    {
        var issues = new List<ContactValidationIssue>();
        var activeContacts = healthRecord.EmergencyContacts.Where(ec => ec.IsActive).ToList();

        // Basic validation
        if (!activeContacts.Any())
        {
            issues.Add(new ContactValidationIssue
            {
                PersonId = healthRecord.PersonId,
                Category = "Missing Contacts",
                Description = "No emergency contacts on file",
                Severity = "Critical",
                RecommendedAction = "Add at least one emergency contact"
            });
        }
        else
        {
            // Check for primary contact
            if (!activeContacts.Any(ec => ec.IsPrimaryContact))
            {
                issues.Add(new ContactValidationIssue
                {
                    PersonId = healthRecord.PersonId,
                    Category = "Primary Contact",
                    Description = "No primary contact designated",
                    Severity = "Critical",
                    RecommendedAction = "Designate one contact as primary"
                });
            }

            if (level >= ValidationLevel.Standard)
            {
                // Validate contact information quality
                foreach (var contact in activeContacts)
                {
                    ValidateContactDetails(contact, issues, healthRecord.PersonId);
                }

                // Check for authorization gaps
                if (!activeContacts.Any(ec => ec.AuthorizedForPickup))
                {
                    issues.Add(new ContactValidationIssue
                    {
                        PersonId = healthRecord.PersonId,
                        Category = "Authorization",
                        Description = "No contacts authorized for pickup",
                        Severity = "Warning",
                        RecommendedAction = "Authorize at least one contact for pickup"
                    });
                }

                if (healthRecord.PersonType == PersonType.Student && !activeContacts.Any(ec => ec.AuthorizedForMedicalDecisions))
                {
                    issues.Add(new ContactValidationIssue
                    {
                        PersonId = healthRecord.PersonId,
                        Category = "Medical Authorization",
                        Description = "No contacts authorized for medical decisions",
                        Severity = "Critical",
                        RecommendedAction = "Authorize at least one contact for medical decisions"
                    });
                }
            }

            if (level == ValidationLevel.Comprehensive)
            {
                // Advanced validation
                ValidateComprehensiveContactInfo(activeContacts, issues, healthRecord.PersonId);
            }
        }

        var validationScore = CalculatePersonValidationScore(activeContacts, issues);

        return new PersonContactValidationResult
        {
            HealthRecordId = healthRecord.Id,
            ValidationScore = validationScore,
            Issues = issues
        };
    }

    private void ValidateContactDetails(EmergencyContact contact, List<ContactValidationIssue> issues, Guid personId)
    {
        if (string.IsNullOrWhiteSpace(contact.Name))
        {
            issues.Add(new ContactValidationIssue
            {
                PersonId = personId,
                Category = "Contact Information",
                Description = $"Contact missing name",
                Severity = "Critical",
                RecommendedAction = "Update contact with full name"
            });
        }

        if (string.IsNullOrWhiteSpace(contact.PrimaryPhone) && string.IsNullOrWhiteSpace(contact.Email))
        {
            issues.Add(new ContactValidationIssue
            {
                PersonId = personId,
                Category = "Contact Information",
                Description = $"Contact {contact.Name} has no phone or email",
                Severity = "Critical",
                RecommendedAction = "Add phone number or email address"
            });
        }

        if (!string.IsNullOrWhiteSpace(contact.PrimaryPhone) && !IsValidPhoneNumber(contact.PrimaryPhone))
        {
            issues.Add(new ContactValidationIssue
            {
                PersonId = personId,
                Category = "Phone Validation",
                Description = $"Contact {contact.Name} has invalid phone format",
                Severity = "Warning",
                RecommendedAction = "Verify and correct phone number format"
            });
        }

        if (!string.IsNullOrWhiteSpace(contact.Email) && !IsValidEmail(contact.Email))
        {
            issues.Add(new ContactValidationIssue
            {
                PersonId = personId,
                Category = "Email Validation",
                Description = $"Contact {contact.Name} has invalid email format",
                Severity = "Warning",
                RecommendedAction = "Verify and correct email address"
            });
        }

        if (contact.Relationship == Relationship.Other && string.IsNullOrWhiteSpace(contact.CustomRelationship))
        {
            issues.Add(new ContactValidationIssue
            {
                PersonId = personId,
                Category = "Relationship",
                Description = $"Contact {contact.Name} needs custom relationship specified",
                Severity = "Warning",
                RecommendedAction = "Specify the custom relationship type"
            });
        }
    }

    private void ValidateComprehensiveContactInfo(List<EmergencyContact> contacts, List<ContactValidationIssue> issues, Guid personId)
    {
        // Check for backup contacts
        if (contacts.Count < 2)
        {
            issues.Add(new ContactValidationIssue
            {
                PersonId = personId,
                Category = "Backup Contacts",
                Description = "Only one emergency contact on file",
                Severity = "Warning",
                RecommendedAction = "Add a second emergency contact for backup"
            });
        }

        // Check for diverse contact methods
        var hasPhone = contacts.Any(c => !string.IsNullOrWhiteSpace(c.PrimaryPhone));
        var hasEmail = contacts.Any(c => !string.IsNullOrWhiteSpace(c.Email));
        
        if (!hasPhone)
        {
            issues.Add(new ContactValidationIssue
            {
                PersonId = personId,
                Category = "Contact Diversity",
                Description = "No phone numbers on file",
                Severity = "Critical",
                RecommendedAction = "Add at least one phone number"
            });
        }

        if (!hasEmail)
        {
            issues.Add(new ContactValidationIssue
            {
                PersonId = personId,
                Category = "Contact Diversity",
                Description = "No email addresses on file",
                Severity = "Warning",
                RecommendedAction = "Add at least one email address"
            });
        }

        // Check for recent updates
        var oldestUpdate = contacts.Min(c => c.LastModifiedAt ?? c.CreatedAt);
        if (oldestUpdate < DateTime.UtcNow.AddYears(-1))
        {
            issues.Add(new ContactValidationIssue
            {
                PersonId = personId,
                Category = "Data Freshness",
                Description = "Emergency contacts not updated in over a year",
                Severity = "Warning",
                RecommendedAction = "Verify and update contact information annually"
            });
        }
    }

    private bool IsValidPhoneNumber(string? phone)
    {
        if (string.IsNullOrWhiteSpace(phone)) return false;
        var phonePattern = @"^\+?[\d\s\-\(\)\.]{10,}$";
        return Regex.IsMatch(phone, phonePattern);
    }

    private bool IsValidEmail(string? email)
    {
        if (string.IsNullOrWhiteSpace(email)) return false;
        var emailPattern = @"^[^@\s]+@[^@\s]+\.[^@\s]+$";
        return Regex.IsMatch(email, emailPattern);
    }

    private int CalculatePersonValidationScore(List<EmergencyContact> contacts, List<ContactValidationIssue> issues)
    {
        var baseScore = 100;
        
        // Deduct points for issues
        foreach (var issue in issues)
        {
            baseScore -= issue.Severity == "Critical" ? 25 : 10;
        }

        // Bonus points for good practices
        if (contacts.Count >= 2) baseScore += 5;
        if (contacts.Any(c => !string.IsNullOrWhiteSpace(c.SecondaryPhone))) baseScore += 5;
        if (contacts.Any(c => !string.IsNullOrWhiteSpace(c.Address))) baseScore += 5;

        return Math.Max(0, Math.Min(100, baseScore));
    }

    private decimal CalculateOverallValidationScore(List<PersonContactValidationResult> validations)
    {
        if (!validations.Any()) return 0;
        return validations.Average(v => v.ValidationScore);
    }

    private decimal CalculateDepartmentValidationScore(List<PersonContactValidationResult> validations)
    {
        if (!validations.Any()) return 0;
        return validations.Average(v => v.ValidationScore);
    }

    private decimal CalculatePersonTypeValidationScore(List<PersonContactValidationResult> validations)
    {
        if (!validations.Any()) return 0;
        return validations.Average(v => v.ValidationScore);
    }

    private int IdentifyDuplicateContacts(List<EmergencyContact> contacts)
    {
        return contacts
            .Where(c => !string.IsNullOrWhiteSpace(c.PrimaryPhone))
            .GroupBy(c => c.PrimaryPhone!.Trim())
            .Count(g => g.Count() > 1);
    }

    private List<string> GenerateRecommendations(List<PersonContactValidationResult> validations, 
        List<DepartmentContactValidationBreakdown> departments, ContactQualityMetrics quality)
    {
        var recommendations = new List<string>();

        var avgScore = validations.Any() ? validations.Average(v => v.ValidationScore) : 0;
        if (avgScore < 70)
        {
            recommendations.Add("Overall contact data quality is below standard. Consider implementing a contact verification campaign.");
        }

        var criticalIssues = validations.Sum(v => v.Issues.Count(i => i.Severity == "Critical"));
        if (criticalIssues > validations.Count * 0.1m)
        {
            recommendations.Add("High number of critical contact issues detected. Prioritize resolving missing or invalid contact information.");
        }

        var lowPerformingDepts = departments.Where(d => d.ValidationScore < 60).ToList();
        if (lowPerformingDepts.Any())
        {
            recommendations.Add($"Departments requiring attention: {string.Join(", ", lowPerformingDepts.Select(d => d.Department))}");
        }

        if (quality.ContactsWithValidPhone / (decimal)quality.TotalContacts < 0.9m)
        {
            recommendations.Add("Consider implementing phone number validation to improve contact reliability.");
        }

        return recommendations;
    }
}