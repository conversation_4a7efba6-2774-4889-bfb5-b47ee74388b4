using MediatR;
using HarmoniHSE360.Domain.Entities;
using HarmoniHSE360.Application.Features.Health.DTOs;

namespace HarmoniHSE360.Application.Features.Health.Queries;

public record GetVaccinationComplianceQuery : IRequest<VaccinationComplianceDto>
{
    public DateTime? FromDate { get; init; }
    public DateTime? ToDate { get; init; }
    public string? Department { get; init; }
    public PersonType? PersonType { get; init; }
    public string? VaccineName { get; init; }
    public bool IncludeInactive { get; init; } = false;
    public bool IncludeExemptions { get; init; } = true;
}