using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace HarmoniHSE360.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddHealthManagementSystem : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "HealthRecords",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    PersonId = table.Column<int>(type: "integer", nullable: false),
                    PersonType = table.Column<int>(type: "integer", nullable: false),
                    DateOfBirth = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    BloodType = table.Column<int>(type: "integer", nullable: true),
                    MedicalNotes = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_HealthRecords", x => x.Id);
                    table.ForeignKey(
                        name: "FK_HealthRecords_Users_PersonId",
                        column: x => x.PersonId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "EmergencyContacts",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    HealthRecordId = table.Column<int>(type: "integer", nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Relationship = table.Column<int>(type: "integer", nullable: false),
                    CustomRelationship = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    PrimaryPhone = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    SecondaryPhone = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    Email = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    Address = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    IsPrimaryContact = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    AuthorizedForPickup = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    AuthorizedForMedicalDecisions = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    ContactOrder = table.Column<int>(type: "integer", nullable: false, defaultValue: 1),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    Notes = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EmergencyContacts", x => x.Id);
                    table.ForeignKey(
                        name: "FK_EmergencyContacts_HealthRecords_HealthRecordId",
                        column: x => x.HealthRecordId,
                        principalTable: "HealthRecords",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "HealthIncidents",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    IncidentId = table.Column<int>(type: "integer", nullable: true),
                    HealthRecordId = table.Column<int>(type: "integer", nullable: false),
                    Type = table.Column<int>(type: "integer", nullable: false),
                    Severity = table.Column<int>(type: "integer", nullable: false),
                    Symptoms = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: false),
                    TreatmentProvided = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: false),
                    TreatmentLocation = table.Column<int>(type: "integer", nullable: false),
                    RequiredHospitalization = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    ParentsNotified = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    ParentNotificationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ReturnToSchoolDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    FollowUpRequired = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    TreatedBy = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    IncidentDateTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    IsResolved = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    ResolutionNotes = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_HealthIncidents", x => x.Id);
                    table.ForeignKey(
                        name: "FK_HealthIncidents_HealthRecords_HealthRecordId",
                        column: x => x.HealthRecordId,
                        principalTable: "HealthRecords",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_HealthIncidents_Incidents_IncidentId",
                        column: x => x.IncidentId,
                        principalTable: "Incidents",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "MedicalConditions",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    HealthRecordId = table.Column<int>(type: "integer", nullable: false),
                    Type = table.Column<int>(type: "integer", nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    Severity = table.Column<int>(type: "integer", nullable: false),
                    TreatmentPlan = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    DiagnosedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    RequiresEmergencyAction = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    EmergencyInstructions = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MedicalConditions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MedicalConditions_HealthRecords_HealthRecordId",
                        column: x => x.HealthRecordId,
                        principalTable: "HealthRecords",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "VaccinationRecords",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    HealthRecordId = table.Column<int>(type: "integer", nullable: false),
                    VaccineName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    DateAdministered = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ExpiryDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    BatchNumber = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    AdministeredBy = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    AdministrationLocation = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    Status = table.Column<int>(type: "integer", nullable: false, defaultValue: 1),
                    Notes = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    IsRequired = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    ExemptionReason = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VaccinationRecords", x => x.Id);
                    table.ForeignKey(
                        name: "FK_VaccinationRecords_HealthRecords_HealthRecordId",
                        column: x => x.HealthRecordId,
                        principalTable: "HealthRecords",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            // Indexes for HealthRecords
            migrationBuilder.CreateIndex(
                name: "IX_HealthRecords_PersonId",
                table: "HealthRecords",
                column: "PersonId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_HealthRecords_PersonType",
                table: "HealthRecords",
                column: "PersonType");

            migrationBuilder.CreateIndex(
                name: "IX_HealthRecords_IsActive",
                table: "HealthRecords",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_HealthRecords_CreatedAt",
                table: "HealthRecords",
                column: "CreatedAt");

            // Indexes for MedicalConditions
            migrationBuilder.CreateIndex(
                name: "IX_MedicalConditions_HealthRecordId",
                table: "MedicalConditions",
                column: "HealthRecordId");

            migrationBuilder.CreateIndex(
                name: "IX_MedicalConditions_Type",
                table: "MedicalConditions",
                column: "Type");

            migrationBuilder.CreateIndex(
                name: "IX_MedicalConditions_Severity",
                table: "MedicalConditions",
                column: "Severity");

            migrationBuilder.CreateIndex(
                name: "IX_MedicalConditions_RequiresEmergencyAction",
                table: "MedicalConditions",
                column: "RequiresEmergencyAction");

            migrationBuilder.CreateIndex(
                name: "IX_MedicalConditions_IsActive",
                table: "MedicalConditions",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_MedicalConditions_CreatedAt",
                table: "MedicalConditions",
                column: "CreatedAt");

            // Indexes for VaccinationRecords
            migrationBuilder.CreateIndex(
                name: "IX_VaccinationRecords_HealthRecordId",
                table: "VaccinationRecords",
                column: "HealthRecordId");

            migrationBuilder.CreateIndex(
                name: "IX_VaccinationRecords_VaccineName",
                table: "VaccinationRecords",
                column: "VaccineName");

            migrationBuilder.CreateIndex(
                name: "IX_VaccinationRecords_Status",
                table: "VaccinationRecords",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_VaccinationRecords_ExpiryDate",
                table: "VaccinationRecords",
                column: "ExpiryDate");

            migrationBuilder.CreateIndex(
                name: "IX_VaccinationRecords_DateAdministered",
                table: "VaccinationRecords",
                column: "DateAdministered");

            migrationBuilder.CreateIndex(
                name: "IX_VaccinationRecords_IsRequired",
                table: "VaccinationRecords",
                column: "IsRequired");

            migrationBuilder.CreateIndex(
                name: "IX_VaccinationRecords_CreatedAt",
                table: "VaccinationRecords",
                column: "CreatedAt");

            // Composite index for vaccination compliance queries
            migrationBuilder.CreateIndex(
                name: "IX_VaccinationRecords_HealthRecordId_VaccineName_Status",
                table: "VaccinationRecords",
                columns: new[] { "HealthRecordId", "VaccineName", "Status" });

            // Indexes for HealthIncidents
            migrationBuilder.CreateIndex(
                name: "IX_HealthIncidents_HealthRecordId",
                table: "HealthIncidents",
                column: "HealthRecordId");

            migrationBuilder.CreateIndex(
                name: "IX_HealthIncidents_IncidentId",
                table: "HealthIncidents",
                column: "IncidentId");

            migrationBuilder.CreateIndex(
                name: "IX_HealthIncidents_Type",
                table: "HealthIncidents",
                column: "Type");

            migrationBuilder.CreateIndex(
                name: "IX_HealthIncidents_Severity",
                table: "HealthIncidents",
                column: "Severity");

            migrationBuilder.CreateIndex(
                name: "IX_HealthIncidents_IncidentDateTime",
                table: "HealthIncidents",
                column: "IncidentDateTime");

            migrationBuilder.CreateIndex(
                name: "IX_HealthIncidents_IsResolved",
                table: "HealthIncidents",
                column: "IsResolved");

            migrationBuilder.CreateIndex(
                name: "IX_HealthIncidents_RequiredHospitalization",
                table: "HealthIncidents",
                column: "RequiredHospitalization");

            migrationBuilder.CreateIndex(
                name: "IX_HealthIncidents_CreatedAt",
                table: "HealthIncidents",
                column: "CreatedAt");

            // Composite index for incident analysis
            migrationBuilder.CreateIndex(
                name: "IX_HealthIncidents_Type_Severity_IncidentDateTime",
                table: "HealthIncidents",
                columns: new[] { "Type", "Severity", "IncidentDateTime" });

            // Indexes for EmergencyContacts
            migrationBuilder.CreateIndex(
                name: "IX_EmergencyContacts_HealthRecordId",
                table: "EmergencyContacts",
                column: "HealthRecordId");

            migrationBuilder.CreateIndex(
                name: "IX_EmergencyContacts_Relationship",
                table: "EmergencyContacts",
                column: "Relationship");

            migrationBuilder.CreateIndex(
                name: "IX_EmergencyContacts_IsPrimaryContact",
                table: "EmergencyContacts",
                column: "IsPrimaryContact");

            migrationBuilder.CreateIndex(
                name: "IX_EmergencyContacts_AuthorizedForPickup",
                table: "EmergencyContacts",
                column: "AuthorizedForPickup");

            migrationBuilder.CreateIndex(
                name: "IX_EmergencyContacts_AuthorizedForMedicalDecisions",
                table: "EmergencyContacts",
                column: "AuthorizedForMedicalDecisions");

            migrationBuilder.CreateIndex(
                name: "IX_EmergencyContacts_IsActive",
                table: "EmergencyContacts",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_EmergencyContacts_CreatedAt",
                table: "EmergencyContacts",
                column: "CreatedAt");

            // Composite index for contact priority
            migrationBuilder.CreateIndex(
                name: "IX_EmergencyContacts_HealthRecordId_ContactOrder_IsActive",
                table: "EmergencyContacts",
                columns: new[] { "HealthRecordId", "ContactOrder", "IsActive" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "EmergencyContacts");

            migrationBuilder.DropTable(
                name: "HealthIncidents");

            migrationBuilder.DropTable(
                name: "MedicalConditions");

            migrationBuilder.DropTable(
                name: "VaccinationRecords");

            migrationBuilder.DropTable(
                name: "HealthRecords");
        }
    }
}