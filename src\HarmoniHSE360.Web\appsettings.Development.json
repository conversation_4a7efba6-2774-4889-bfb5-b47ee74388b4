{"ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=HarmoniHSE360_Dev;Username=postgres;Password=***********"}, "Jwt": {"Key": "YourSuperSecretDevelopmentJwtKeyThatIsAtLeast32CharactersLong!", "Issuer": "HarmoniHSE360", "Audience": "HarmoniHSE360Users", "ExpirationMinutes": 1440, "RefreshTokenExpirationDays": 30}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Information"}}, "AllowedHosts": "*", "Cors": {"AllowedOrigins": "http://localhost:5173,http://localhost:3000"}, "Kestrel": {"Endpoints": {"Http": {"Url": "http://localhost:5000"}}}, "UseSpaProxy": false, "DataSeeding": {"SeedIncidents": true, "ReSeedIncidents": false, "ReSeedUsers": false, "SeedPPEData": true, "ReSeedPPEData": true, "SeedPPEItems": true, "ReSeedPPEItems": false}}