{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=harmonihse360_dev;Username=********;Password=********", "Redis": "localhost:6379"}, "Jwt": {"Key": "YourSecretKeyHereWhichShouldBeAtLeast32CharactersLongForSecurity", "Issuer": "HarmoniHSE360", "Audience": "HarmoniHSE360Users", "ExpirationMinutes": "60", "RefreshTokenExpirationDays": "7"}, "Seq": {"ServerUrl": "http://localhost:5341"}, "DataSeeding": {"SeedIncidents": true, "ReSeedIncidents": false, "ReSeedUsers": false, "SeedPPEData": true, "ReSeedPPEData": false, "SeedPPEItems": true, "ReSeedPPEItems": false}}